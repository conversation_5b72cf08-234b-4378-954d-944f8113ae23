{"basics": {"name": "Vishnu K<PERSON>", "title": "Senior software Engineer @HCLSoftware", "profilePicURL": "https://d2ajlz7o3p8ddv.cloudfront.net/media/user_profile/Screenshot_2024-12-04_233305.png", "Experience": "10 Years", "contact": {"phones": ["+************", "+************"], "email": "<EMAIL>", "linkedin": "https://www.linkedin.com/in/vishnu-kr-937705b7/", "otherProfiles": [{"name": "Instagram", "icon": "assets/instagram.png", "url": "", "category": "social_media"}, {"name": "github", "icon": "assets/github.png", "url": "", "category": "tech"}], "address": {"street": "Kuttantharappel House, Pullurampara Post", "city": "Kozhikode", "state": "Kerala", "zip": "673603", "country": "India"}}, "dob": "September 23", "objective": "Seeking a challenging position where my knowledge, talent, hard work, dedication, and sincerity can create significant value. I am looking for an opportunity to work with the best minds of the profession as well as upgrade my skills and knowledge about emerging technologies."}, "education": [{"degree": "BE ECE", "year": 2014, "percentage": 76.7, "institution": "AMS Engineering College", "university": "Anna University Chennai"}, {"degree": "HSE(+2)", "year": 2010, "percentage": 78, "institution": "Kunnamangalam HSS", "board": "SBSC"}, {"degree": "SSLC(10)", "year": 2008, "percentage": 85, "institution": "<PERSON><PERSON>’s HS Pullurampara", "board": "SBSC"}], "certifications": [{"title": "Internship In Embedded C", "organization": "Keltron", "duration": "3 Months"}, {"title": "Internship In Java", "organization": "Bodhi Info Solutions Pvt.Ltd", "duration": "6 Months"}], "experience": [{"company": "HCLSoftare", "position": "Senior Software Engineer 3", "icon": "/assets/hclSoftware.png", "duration": "2022-09 - Present", "projects": [{"name": "BigFix - MCM and MDM", "client": "HCL BigFix", "teamSize": 15, "techStack": {"languages": [{"name": "Go", "design": "85", "develop": "90", "deployment": "90", "profiling": "70"}, {"name": "JavaScript", "design": "75", "develop": "80", "deployment": "70", "profiling": "50"}, {"name": "TypeScript", "design": "75", "develop": "80", "deployment": "70", "profiling": "50"}, {"name": "ShellScript", "design": "50", "develop": "50", "deployment": "85", "profiling": "40"}], "libraries": [{"name": "goDog", "experience": "<PERSON><PERSON><PERSON>"}, {"name": "Gorilla-Mux", "experience": "<PERSON><PERSON><PERSON>"}, {"name": "ReactJs 18+", "experience": "Expert"}], "frameworks": [{"name": "Gin", "experience": "Moderate"}], "databases": [{"name": "BoltDB", "experience": "Moderate"}, {"name": "Postgres", "experience": "Moderate"}, {"name": "MongoDB", "experience": "<PERSON><PERSON><PERSON>"}], "cloud": [{"name": "Azure", "experience": "Advanced Beginner"}, {"name": "AWS", "experience": "<PERSON><PERSON><PERSON>"}], "others": [{"name": "<PERSON><PERSON><PERSON>", "experience": "Moderate"}, {"name": "<PERSON>er", "experience": "Moderate"}, {"name": "Kubernetes", "experience": "Moderate"}, {"name": "<PERSON><PERSON>", "experience": "<PERSON><PERSON><PERSON>"}]}, "role": "Full-stack Engineer", "responsibilities": ["Research and Development of new features", "Design, Implement and Demo of features", "Guiding and Mentoring of Junior Developers", "Presenting POCs in customer meetings and well as internal meetings", "Attending and Contributing in Technical Discussions and Meetings", "Defect Fixing and Code Reviews", "Customer Interaction and Management"], "projectManagement": {"method": "Agile", "tool": "<PERSON><PERSON>", "familiarity": "Moderate", "framework": "Scrum"}}, {"name": "BigFix - SaaS Core Services", "client": "HCL BigFix", "teamSize": 8, "techStack": {"languages": [{"name": "Go", "design": "85", "develop": "90", "deployment": "90", "profiling": "70"}, {"name": "JavaScript", "design": "75", "develop": "80", "deployment": "70", "profiling": "50"}, {"name": "TypeScript", "design": "75", "develop": "80", "deployment": "70", "profiling": "50"}, {"name": "ShellScript", "design": "50", "develop": "50", "deployment": "85", "profiling": "40"}], "libraries": [{"name": "k6", "experience": "<PERSON><PERSON><PERSON>"}, {"name": "saas-utils", "experience": "Creator"}, {"name": "ReactJs 18+", "experience": "Expert"}, {"name": "Zustand", "experience": "Moderate"}], "frameworks": [{"name": "Go-chi", "experience": "Moderate"}, {"name": "NextJS 14", "experience": "<PERSON><PERSON>"}], "databases": [{"name": "Postgres", "experience": "Moderate"}], "cloud": [{"name": "AWS", "experience": "<PERSON><PERSON><PERSON>"}, {"name": "GCP", "experience": "Moderate"}], "others": [{"name": "<PERSON><PERSON><PERSON>", "experience": "Moderate"}, {"name": "<PERSON>er", "experience": "Moderate"}, {"name": "Kubernetes", "experience": "Moderate"}, {"name": "<PERSON><PERSON>", "experience": "<PERSON><PERSON><PERSON>"}]}, "role": "Full-stack Engineer", "responsibilities": ["Research and Development of new features", "Design, Implement and Demo of features", "Guiding and Mentoring of Junior Developers", "Presenting POCs in customer meetings and well as internal meetings", "Attending and Contributing in Technical Discussions and Meetings", "Defect Fixing and Code Reviews", "Customer Interaction and Management"], "projectManagement": {"method": "Agile", "tool": "<PERSON><PERSON>", "familiarity": "Moderate", "framework": "Scrum"}}]}, {"company": "HTC Global Services", "icon": "/assets/htc.png", "position": "Lead Engineer", "duration": "2021-06 to 2022-09", "projects": [{"name": "CMRL - M-Ticket Application", "client": "Ford Motor Company (CMRL)", "teamSize": 15, "techStack": {"languages": [{"name": "Java 11", "design": "85", "develop": "90", "deployment": "80", "profiling": "75"}, {"name": "JavaScript", "design": "75", "develop": "80", "deployment": "70", "profiling": "50"}, {"name": "ShellScript", "design": "40", "develop": "50", "deployment": "80", "profiling": "40"}], "libraries": [{"name": "Pi4j", "experience": "Expert"}], "frameworks": [{"name": "Angular 12", "experience": "Moderate"}, {"name": "Spring Boot 2", "experience": "Expert"}, {"name": "Spring WebFlux", "experience": "Moderate"}, {"name": "JavaFX", "experience": "Moderate"}], "databases": [{"name": "SQL Server", "experience": "Moderate"}, {"name": "SQLite", "experience": "Moderate"}], "cloud": [{"name": "Azure", "experience": "Advanced Beginner"}], "others": [{"name": "<PERSON><PERSON><PERSON>", "experience": "Moderate"}, {"name": "<PERSON>er", "experience": "Moderate"}, {"name": "Openshift", "experience": "<PERSON><PERSON><PERSON>"}]}, "role": "ECU Team Lead", "responsibilities": ["Architecting backend and Raspberry Pi services", "Hardware integration with Raspberry Pi", "Managing ECU team", "Deployment and bug fixing", "ECU firmware re-architecture and OTA updates"], "projectManagement": {"method": "Agile", "tool": "<PERSON><PERSON>", "familiarity": "Moderate", "framework": "Scrum"}}]}, {"company": "Cognizant Technology Solutions", "position": "Associate", "icon": "", "duration": "2019-07 - 2021-06", "projects": [{"name": "ProDeploy Enterprise", "client": "Dell Technologies", "teamSize": 18, "techStack": {"languages": [{"name": "Java 8", "design": "85", "develop": "90", "deployment": "80", "profiling": "75"}], "libraries": [], "frameworks": [{"name": "Angular 10", "experience": "Expert"}, {"name": "Spring Boot", "experience": "Expert"}], "databases": [{"name": "OracleDB", "experience": "Moderate"}, {"name": "MongoDB", "experience": "Moderate"}], "cloud": [{"name": "Pivotal Cloud", "experience": "Moderate"}], "others": [{"name": "<PERSON><PERSON><PERSON>", "experience": "Moderate"}, {"name": "<PERSON>er", "experience": "Moderate"}, {"name": "PCF", "experience": "Moderate"}]}, "role": "Squad Lead", "responsibilities": ["Requirement analysis with onsite teams", "TDD-based development", "SQL and Mongo aggregation", "UI/UX design", "Microservices development"]}]}, {"company": "NeST Information Technologies", "position": "Software Engineer", "duration": "2017-06 - 2019-06", "projects": [{"name": "LOS - Loan Origination System", "teamSize": 7, "techStack": {"languages": [{"name": "Java 8", "experience": "Expert"}], "libraries": [{"name": "<PERSON><PERSON>", "experience": "Moderate"}, {"name": "Rabbit MQ", "experience": "Moderate"}], "frameworks": [{"name": "Angular 5", "experience": "Expert"}, {"name": "Spring Boot", "experience": "Expert"}, {"name": "Spring Cloud Stream", "experience": "Moderate"}], "databases": [], "cloud": [], "others": []}, "role": "Full-stack Developer", "responsibilities": ["Microservice architecture and OAuth2", "Client demos", "Deployment and testing"]}, {"name": "NETS - Enterprise Tool Suite", "teamSize": 15, "techStack": {"languages": [{"name": "Java 8", "experience": "Expert"}], "libraries": [{"name": "<PERSON><PERSON>", "experience": "Moderate"}, {"name": "Rabbit MQ", "experience": "Moderate"}], "frameworks": [{"name": "Angular 5", "experience": "Expert"}, {"name": "Spring Boot", "experience": "Expert"}, {"name": "Spring Cloud Stream", "experience": "Moderate"}], "databases": [], "cloud": [], "others": []}, "role": "Full-stack Developer", "responsibilities": ["Architecture template creation", "Code reviews", "Backlog and requirement analysis"]}, {"name": "UPS - Unified Payment Solution", "techStack": {"languages": [{"name": "Java 8", "experience": "Expert"}], "libraries": [{"name": "<PERSON><PERSON>", "experience": "Moderate"}, {"name": "Rabbit MQ", "experience": "Moderate"}], "frameworks": [{"name": "Angular 5", "experience": "Expert"}, {"name": "Spring Boot", "experience": "Expert"}, {"name": "Spring Cloud Stream", "experience": "Moderate"}], "databases": [], "cloud": [], "others": []}, "role": "Full-stack Developer", "responsibilities": ["Integration with Oracle FlexCube", "Form and table configuration using NETS"]}]}, {"company": "Bodhi Info Solutions Pvt.Ltd", "position": "Software Engineer", "duration": "2015-10 - 2017-06", "projects": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Android App", "techStack": {"languages": [{"name": "Java 7", "experience": "Expert"}], "libraries": [{"name": "Android SDK", "experience": "Expert"}], "frameworks": [], "databases": [], "cloud": [], "others": []}, "role": "Android Developer", "responsibilities": ["UI implementation", "Deployment", "Training interns"]}, {"name": "Mobinion", "type": "Android App", "techStack": {"languages": [{"name": "Java 7", "experience": "Expert"}], "libraries": [{"name": "Android SDK", "experience": "Expert"}], "frameworks": [], "databases": [], "cloud": [], "others": []}, "role": "Android Developer", "responsibilities": ["UI implementation", "Deployment", "MVP architecture", "Training interns"]}, {"name": "Browntown", "type": "iPad App", "techStack": {"languages": [{"name": "React Native", "experience": "Expert"}], "libraries": [{"name": "Xcode", "experience": "Moderate"}], "frameworks": [], "databases": [], "cloud": [], "others": []}, "role": "Mobile App Developer", "responsibilities": ["Live menu ordering", "Deployment"]}]}], "declaration": {"text": "I hereby declare that the information provided above is correct to the best of my knowledge.", "name": "Vishnu K<PERSON>"}}